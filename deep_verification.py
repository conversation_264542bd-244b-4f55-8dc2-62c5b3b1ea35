#!/usr/bin/env python3
"""
فحص عميق للتأكد من حذف شاشة تسجيل الدخول
"""

import os
import sys
import struct
import re
from pathlib import Path

class DeepLoginVerifier:
    def __init__(self, original_path, modified_path):
        self.original_path = original_path
        self.modified_path = modified_path
        self.original_data = None
        self.modified_data = None
        
    def load_files(self):
        """تحميل الملفين للمقارنة"""
        try:
            with open(self.original_path, 'rb') as f:
                self.original_data = f.read()
            with open(self.modified_path, 'rb') as f:
                self.modified_data = f.read()
            print("✅ تم تحميل الملفين بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل الملفين: {e}")
            return False
    
    def find_login_ui_elements(self, data):
        """البحث عن عناصر واجهة تسجيل الدخول"""
        ui_patterns = [
            b"Login",
            b"login",
            b"Username",
            b"username", 
            b"Password",
            b"password",
            b"Sign In",
            b"signin",
            b"Log In",
            b"Enter Password",
            b"Enter Username",
            b"Authentication",
            b"auth",
            b"credential",
            b"QLineEdit",  # Qt input fields
            b"QPushButton", # Qt buttons
            b"QLabel",     # Qt labels
            b"QDialog",    # Qt dialogs
            b"LoginDialog",
            b"AuthDialog"
        ]
        
        found_elements = {}
        for pattern in ui_patterns:
            positions = []
            start = 0
            while True:
                pos = data.find(pattern, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            if positions:
                found_elements[pattern.decode('utf-8', errors='ignore')] = positions
        
        return found_elements
    
    def analyze_jump_modifications(self):
        """تحليل تعديلات القفزات المشروطة"""
        if not self.original_data or not self.modified_data:
            return False
        
        print("🔍 تحليل تعديلات القفزات...")
        
        modifications = []
        for i in range(len(self.original_data)):
            if i < len(self.modified_data):
                orig_byte = self.original_data[i]
                mod_byte = self.modified_data[i]
                
                if orig_byte != mod_byte:
                    # تحليل نوع التعديل
                    modification_type = "غير معروف"
                    effectiveness = "منخفض"
                    
                    if orig_byte == 0x74 and mod_byte == 0xEB:  # JE -> JMP
                        modification_type = "تحويل JE إلى JMP (تخطي شرط المساواة)"
                        effectiveness = "عالي"
                    elif orig_byte == 0x75 and mod_byte == 0xEB:  # JNE -> JMP
                        modification_type = "تحويل JNE إلى JMP (تخطي شرط عدم المساواة)"
                        effectiveness = "عالي"
                    elif orig_byte == 0x74 and mod_byte == 0x90:  # JE -> NOP
                        modification_type = "تحويل JE إلى NOP (إلغاء القفزة)"
                        effectiveness = "متوسط"
                    elif orig_byte == 0x75 and mod_byte == 0x90:  # JNE -> NOP
                        modification_type = "تحويل JNE إلى NOP (إلغاء القفزة)"
                        effectiveness = "متوسط"
                    
                    modifications.append({
                        'offset': i,
                        'hex_offset': f"0x{i:08X}",
                        'original': orig_byte,
                        'modified': mod_byte,
                        'type': modification_type,
                        'effectiveness': effectiveness
                    })
        
        return modifications
    
    def check_auto_login_status(self):
        """فحص حالة تسجيل الدخول التلقائي"""
        print("🔍 فحص حالة Auto Login...")
        
        # البحث عن "Auto Login" في كلا الملفين
        auto_login_orig = self.original_data.find(b"Auto Login")
        auto_login_mod = self.modified_data.find(b"Auto Login")
        
        if auto_login_orig != -1 and auto_login_mod != -1:
            print(f"📍 وُجد 'Auto Login' في الموقع: 0x{auto_login_orig:08X}")
            
            # فحص البايتات المحيطة للبحث عن تغييرات
            start = max(0, auto_login_orig - 50)
            end = min(len(self.original_data), auto_login_orig + 100)
            
            orig_context = self.original_data[start:end]
            mod_context = self.modified_data[start:end]
            
            if orig_context != mod_context:
                print("✅ تم العثور على تغييرات في منطقة Auto Login")
                return True
            else:
                print("⚠️ لم يتم العثور على تغييرات في منطقة Auto Login")
                return False
        
        return False
    
    def verify_login_bypass_effectiveness(self):
        """التحقق من فعالية تخطي تسجيل الدخول"""
        print("🧪 التحقق من فعالية تخطي تسجيل الدخول...")
        print("=" * 60)
        
        # تحليل التعديلات
        modifications = self.analyze_jump_modifications()
        
        if not modifications:
            print("❌ لم يتم العثور على أي تعديلات!")
            return False
        
        print(f"📊 تم العثور على {len(modifications)} تعديل:")
        
        high_effectiveness_count = 0
        for i, mod in enumerate(modifications, 1):
            print(f"\n{i}. الموقع {mod['hex_offset']}:")
            print(f"   التغيير: {mod['original']:02X} -> {mod['modified']:02X}")
            print(f"   النوع: {mod['type']}")
            print(f"   الفعالية: {mod['effectiveness']}")
            
            if mod['effectiveness'] == "عالي":
                high_effectiveness_count += 1
        
        # تحليل عناصر واجهة تسجيل الدخول
        print(f"\n{'='*60}")
        print("🔍 تحليل عناصر واجهة تسجيل الدخول:")
        
        orig_ui = self.find_login_ui_elements(self.original_data)
        mod_ui = self.find_login_ui_elements(self.modified_data)
        
        print(f"\nالملف الأصلي: {len(orig_ui)} عنصر واجهة")
        print(f"الملف المعدل: {len(mod_ui)} عنصر واجهة")
        
        # مقارنة عناصر الواجهة
        ui_changes = False
        for element in orig_ui:
            if element in mod_ui:
                if len(orig_ui[element]) != len(mod_ui[element]):
                    print(f"⚠️ تغيير في عدد مرات ظهور '{element}': {len(orig_ui[element])} -> {len(mod_ui[element])}")
                    ui_changes = True
        
        # فحص Auto Login
        auto_login_changed = self.check_auto_login_status()
        
        # تقييم النتائج
        print(f"\n{'='*60}")
        print("📋 تقييم النتائج:")
        
        success_indicators = 0
        total_indicators = 4
        
        if high_effectiveness_count >= 3:
            print("✅ تعديلات عالية الفعالية: نعم")
            success_indicators += 1
        else:
            print("❌ تعديلات عالية الفعالية: لا")
        
        if len(modifications) >= 5:
            print("✅ عدد كافي من التعديلات: نعم")
            success_indicators += 1
        else:
            print("❌ عدد كافي من التعديلات: لا")
        
        if auto_login_changed:
            print("✅ تغييرات في منطقة Auto Login: نعم")
            success_indicators += 1
        else:
            print("❌ تغييرات في منطقة Auto Login: لا")
        
        # فحص سلامة الملف
        if len(self.original_data) == len(self.modified_data):
            print("✅ سلامة حجم الملف: نعم")
            success_indicators += 1
        else:
            print("❌ سلامة حجم الملف: لا")
        
        success_rate = (success_indicators / total_indicators) * 100
        
        print(f"\n🎯 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print("🎉 النتيجة: تخطي تسجيل الدخول ناجح بشكل كبير!")
            print("✅ من المتوقع أن يفتح التطبيق الصفحة الرئيسية مباشرة")
        elif success_rate >= 50:
            print("⚠️ النتيجة: تخطي تسجيل الدخول ناجح جزئياً")
            print("🔧 قد تحتاج لتعديلات إضافية")
        else:
            print("❌ النتيجة: تخطي تسجيل الدخول غير ناجح")
            print("🔧 يحتاج لمراجعة وتعديل الطريقة")
        
        return success_rate >= 75

def main():
    original_file = "setup.exe"
    modified_file = "setup_bypassed.exe"
    
    if not os.path.exists(original_file):
        print(f"❌ الملف الأصلي غير موجود: {original_file}")
        sys.exit(1)
    
    if not os.path.exists(modified_file):
        print(f"❌ الملف المعدل غير موجود: {modified_file}")
        sys.exit(1)
    
    verifier = DeepLoginVerifier(original_file, modified_file)
    
    if verifier.load_files():
        success = verifier.verify_login_bypass_effectiveness()
        
        print(f"\n{'='*60}")
        if success:
            print("🎉 الخلاصة: تم حذف/تخطي شاشة تسجيل الدخول بنجاح!")
            print("📝 التوصية: جرب تشغيل setup_bypassed.exe")
        else:
            print("❌ الخلاصة: لم يتم حذف شاشة تسجيل الدخول بشكل كامل")
            print("🔧 التوصية: مراجعة الطريقة وإجراء تعديلات إضافية")

if __name__ == "__main__":
    main()
