@echo off
echo ========================================
echo تنظيف المجلد - الاحتفاظ بالملف المطلوب فقط
echo ========================================
echo.
echo الملف المطلوب: setup_bypassed.exe
echo.
echo الملفات التي سيتم حذفها:
echo   - جميع ملفات Python (.py)
echo   - جميع التقارير (.md)
echo   - الملف الأصلي setup.exe
echo   - النسخة الاحتياطية
echo   - مجلدات الأدوات
echo.
echo هل تريد المتابعة؟ (Y/N)
set /p choice=
if /i "%choice%" neq "Y" goto :end
echo.
echo بدء التنظيف...
echo.

REM حذف ملفات Python
del /q *.py 2>nul
echo ✓ تم حذف ملفات Python

REM حذف التقارير
del /q *.md 2>nul
echo ✓ تم حذف التقارير

REM حذف ملفات bat الأخرى
del /q run_bypassed_app.bat 2>nul
del /q compare_apps.bat 2>nul
echo ✓ تم حذف سكريبتات الاختبار

REM حذف الملف الأصلي
del /q setup.exe 2>nul
echo ✓ تم حذف الملف الأصلي

REM حذف النسخة الاحتياطية
del /q setup.exe.backup 2>nul
echo ✓ تم حذف النسخة الاحتياطية

REM حذف المجلدات
rmdir /s /q analysis 2>nul
rmdir /s /q backup 2>nul
rmdir /s /q tools 2>nul
rmdir /s /q test_environment 2>nul
echo ✓ تم حذف المجلدات

echo.
echo ========================================
echo تم التنظيف بنجاح!
echo ========================================
echo.
echo الملفات المتبقية:
dir /b
echo.
echo الملف الوحيد المطلوب: setup_bypassed.exe
echo هذا هو التطبيق المعدل الذي يتخطى شاشة تسجيل الدخول
echo.

:end
pause
