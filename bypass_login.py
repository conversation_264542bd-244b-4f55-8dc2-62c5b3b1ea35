#!/usr/bin/env python3
"""
أداة تخطي تسجيل الدخول - للأغراض التعليمية والأخلاقية فقط
"""

import os
import sys
import struct
import shutil
from pathlib import Path

class LoginBypass:
    def __init__(self, file_path):
        self.file_path = file_path
        self.backup_path = f"{file_path}.backup"
        self.file_data = None
        
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✓ تم إنشاء نسخة احتياطية: {self.backup_path}")
            return True
        except Exception as e:
            print(f"✗ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def load_file(self):
        """تحميل الملف"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = bytearray(f.read())
            print(f"✓ تم تحميل الملف: {self.file_path}")
            return True
        except Exception as e:
            print(f"✗ خطأ في تحميل الملف: {e}")
            return False
    
    def save_file(self, output_path=None):
        """حفظ الملف المعدل"""
        if output_path is None:
            output_path = self.file_path
            
        try:
            with open(output_path, 'wb') as f:
                f.write(self.file_data)
            print(f"✓ تم حفظ الملف المعدل: {output_path}")
            return True
        except Exception as e:
            print(f"✗ خطأ في حفظ الملف: {e}")
            return False
    
    def find_pattern(self, pattern):
        """البحث عن نمط في الملف"""
        if isinstance(pattern, str):
            pattern = pattern.encode()
        
        positions = []
        start = 0
        while True:
            pos = self.file_data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        return positions
    
    def find_login_checks(self):
        """البحث عن فحوصات تسجيل الدخول"""
        patterns = [
            b"Auto Login",
            b"login",
            b"Login",
            b"LOGIN",
            b"auth",
            b"Auth",
            b"AUTH",
            b"password",
            b"Password",
            b"PASSWORD",
            b"username",
            b"Username",
            b"USERNAME"
        ]
        
        found_patterns = {}
        for pattern in patterns:
            positions = self.find_pattern(pattern)
            if positions:
                found_patterns[pattern.decode()] = positions
        
        return found_patterns
    
    def patch_auto_login(self):
        """تفعيل تسجيل الدخول التلقائي"""
        # البحث عن "Auto Login Enabled"
        auto_login_pattern = b"Auto Login Enabled"
        positions = self.find_pattern(auto_login_pattern)
        
        if positions:
            print(f"🔍 وُجد 'Auto Login Enabled' في {len(positions)} موقع")
            
            # البحث عن القيم المنطقية القريبة
            for pos in positions:
                # فحص البايتات المحيطة
                start = max(0, pos - 100)
                end = min(len(self.file_data), pos + 100)
                context = self.file_data[start:end]
                
                print(f"📍 الموقع {pos}: فحص السياق...")
                
                # البحث عن قيم منطقية محتملة (0x00 = false, 0x01 = true)
                for i in range(len(context)):
                    if context[i] in [0x00, 0x01]:
                        actual_pos = start + i
                        print(f"   🎯 قيمة منطقية محتملة في الموقع {actual_pos}: {context[i]}")
        
        return positions
    
    def patch_login_bypass(self):
        """تطبيق patch لتخطي تسجيل الدخول"""
        print("🔧 بدء عملية تخطي تسجيل الدخول...")
        
        # البحث عن أنماط تسجيل الدخول
        login_patterns = self.find_login_checks()
        
        if not login_patterns:
            print("⚠️ لم يتم العثور على أنماط تسجيل دخول واضحة")
            return False
        
        print("🔍 الأنماط الموجودة:")
        for pattern, positions in login_patterns.items():
            print(f"   {pattern}: {len(positions)} موقع")
        
        # محاولة تفعيل Auto Login
        auto_login_positions = self.patch_auto_login()
        
        # البحث عن jump conditions محتملة
        # في x86, conditional jumps مثل JE (74), JNE (75), JZ (74), JNZ (75)
        jump_opcodes = [0x74, 0x75, 0x0F84, 0x0F85]  # JE, JNE, JZ, JNZ
        
        modifications = 0
        for i in range(len(self.file_data) - 1):
            if self.file_data[i] in [0x74, 0x75]:  # JE/JNE opcodes
                # تحويل JE إلى JMP أو NOP
                original = self.file_data[i]
                self.file_data[i] = 0xEB  # JMP short
                modifications += 1
                print(f"🔄 تم تعديل jump في الموقع {i}: {original:02X} -> EB")
                
                if modifications >= 5:  # تحديد عدد التعديلات لتجنب كسر البرنامج
                    break
        
        print(f"✅ تم إجراء {modifications} تعديل")
        return modifications > 0
    
    def analyze_and_bypass(self):
        """تحليل وتخطي تسجيل الدخول"""
        print("🚀 بدء عملية التحليل والتخطي...")
        
        # إنشاء نسخة احتياطية
        if not self.create_backup():
            return False
        
        # تحميل الملف
        if not self.load_file():
            return False
        
        # تطبيق التخطي
        if self.patch_login_bypass():
            # حفظ الملف المعدل
            modified_path = f"{self.file_path.rsplit('.', 1)[0]}_bypassed.exe"
            if self.save_file(modified_path):
                print(f"🎉 تم إنشاء الملف المعدل: {modified_path}")
                print("⚠️ تأكد من اختبار الملف في بيئة آمنة أولاً!")
                return True
        
        return False

def main():
    if len(sys.argv) != 2:
        print("الاستخدام: python bypass_login.py <path_to_exe>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"الملف غير موجود: {file_path}")
        sys.exit(1)
    
    bypasser = LoginBypass(file_path)
    success = bypasser.analyze_and_bypass()
    
    if success:
        print("\n✅ تمت العملية بنجاح!")
        print("📝 ملاحظات:")
        print("   - تم إنشاء نسخة احتياطية من الملف الأصلي")
        print("   - اختبر الملف المعدل في بيئة آمنة")
        print("   - هذا للأغراض التعليمية فقط")
    else:
        print("\n❌ فشلت العملية")

if __name__ == "__main__":
    main()
