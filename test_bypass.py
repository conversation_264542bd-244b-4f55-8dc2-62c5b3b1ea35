#!/usr/bin/env python3
"""
أداة اختبار تخطي تسجيل الدخول
"""

import os
import sys
import subprocess
import time
from pathlib import Path

class BypassTester:
    def __init__(self, exe_path):
        self.exe_path = exe_path
        
    def check_file_exists(self):
        """التحقق من وجود الملف"""
        if not os.path.exists(self.exe_path):
            print(f"❌ الملف غير موجود: {self.exe_path}")
            return False
        print(f"✅ الملف موجود: {self.exe_path}")
        return True
    
    def get_file_info(self):
        """الحصول على معلومات الملف"""
        try:
            stat = os.stat(self.exe_path)
            print(f"📊 معلومات الملف:")
            print(f"   الحجم: {stat.st_size} بايت")
            print(f"   تاريخ التعديل: {time.ctime(stat.st_mtime)}")
            return True
        except Exception as e:
            print(f"❌ خطأ في قراءة معلومات الملف: {e}")
            return False
    
    def test_execution(self):
        """اختبار تشغيل الملف"""
        print("🧪 اختبار تشغيل الملف...")
        print("⚠️ تأكد من تشغيل هذا في بيئة آمنة!")
        
        response = input("هل تريد المتابعة؟ (y/N): ")
        if response.lower() != 'y':
            print("❌ تم إلغاء الاختبار")
            return False
        
        try:
            # محاولة تشغيل الملف مع timeout
            print("🚀 بدء تشغيل التطبيق...")
            process = subprocess.Popen(
                [self.exe_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            
            # انتظار لمدة 5 ثوان
            time.sleep(5)
            
            # التحقق من حالة العملية
            if process.poll() is None:
                print("✅ التطبيق يعمل!")
                print("📝 ملاحظة: تحقق من نافذة التطبيق لرؤية النتيجة")
                
                # إنهاء العملية
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ التطبيق توقف مع كود الخروج: {process.returncode}")
                if stderr:
                    print(f"خطأ: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
            return False
    
    def create_test_script(self):
        """إنشاء سكريبت اختبار"""
        script_content = f"""@echo off
echo ========================================
echo اختبار تطبيق تخطي تسجيل الدخول
echo ========================================
echo.
echo الملف: {self.exe_path}
echo.
echo تحذير: تأكد من تشغيل هذا في بيئة آمنة!
echo.
pause
echo.
echo بدء تشغيل التطبيق...
start "" "{self.exe_path}"
echo.
echo تم تشغيل التطبيق. تحقق من النافذة الجديدة.
echo إذا تم تخطي شاشة تسجيل الدخول، فقد نجحت العملية!
echo.
pause
"""
        
        with open("test_bypass.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ تم إنشاء سكريبت الاختبار: test_bypass.bat")
        return True
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("🔬 بدء الاختبار الشامل...")
        print("=" * 50)
        
        # فحص وجود الملف
        if not self.check_file_exists():
            return False
        
        # معلومات الملف
        if not self.get_file_info():
            return False
        
        # إنشاء سكريبت الاختبار
        self.create_test_script()
        
        # اختبار التشغيل
        print("\n" + "=" * 50)
        print("🧪 اختبار التشغيل:")
        success = self.test_execution()
        
        print("\n" + "=" * 50)
        print("📋 ملخص النتائج:")
        if success:
            print("✅ الاختبار نجح!")
            print("📝 التوصيات:")
            print("   1. تحقق من أن التطبيق يفتح الصفحة الرئيسية مباشرة")
            print("   2. تأكد من عمل جميع الوظائف بشكل صحيح")
            print("   3. راقب أي رسائل خطأ أو سلوك غير طبيعي")
        else:
            print("❌ الاختبار فشل!")
            print("🔧 اقتراحات:")
            print("   1. تحقق من صحة التعديلات")
            print("   2. جرب تشغيل الملف الأصلي للمقارنة")
            print("   3. استخدم أدوات تصحيح أكثر تقدماً")
        
        return success

def main():
    if len(sys.argv) != 2:
        print("الاستخدام: python test_bypass.py <path_to_exe>")
        print("مثال: python test_bypass.py setup_bypassed.exe")
        sys.exit(1)
    
    exe_path = sys.argv[1]
    tester = BypassTester(exe_path)
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
