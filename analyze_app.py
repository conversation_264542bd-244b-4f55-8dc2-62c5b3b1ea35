#!/usr/bin/env python3
"""
أداة تحليل التطبيقات للهندسة العكسية الأخلاقية
"""

import os
import sys
import hashlib
import subprocess
import struct
from pathlib import Path

class PEAnalyzer:
    def __init__(self, file_path):
        self.file_path = file_path
        self.file_data = None
        
    def load_file(self):
        """تحميل الملف للتحليل"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            print(f"✓ تم تحميل الملف: {self.file_path}")
            return True
        except Exception as e:
            print(f"✗ خطأ في تحميل الملف: {e}")
            return False
    
    def get_file_info(self):
        """الحصول على معلومات أساسية عن الملف"""
        if not self.file_data:
            return None
            
        info = {
            'size': len(self.file_data),
            'md5': hashlib.md5(self.file_data).hexdigest(),
            'sha256': hashlib.sha256(self.file_data).hexdigest(),
        }
        
        # فحص PE header
        if self.file_data[:2] == b'MZ':
            info['type'] = 'PE Executable'
            # قراءة PE offset
            pe_offset = struct.unpack('<I', self.file_data[60:64])[0]
            if pe_offset < len(self.file_data) - 4:
                pe_signature = self.file_data[pe_offset:pe_offset+4]
                if pe_signature == b'PE\x00\x00':
                    info['valid_pe'] = True
                else:
                    info['valid_pe'] = False
        
        return info
    
    def find_strings(self, min_length=4):
        """البحث عن النصوص في الملف"""
        if not self.file_data:
            return []
            
        strings = []
        current_string = ""
        
        for byte in self.file_data:
            if 32 <= byte <= 126:  # ASCII printable characters
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        # إضافة آخر نص إذا كان طويلاً بما فيه الكفاية
        if len(current_string) >= min_length:
            strings.append(current_string)
            
        return strings
    
    def find_login_related_strings(self):
        """البحث عن نصوص متعلقة بتسجيل الدخول"""
        strings = self.find_strings()
        login_keywords = [
            'login', 'password', 'username', 'auth', 'signin', 'logon',
            'user', 'pass', 'credential', 'token', 'session',
            'تسجيل', 'دخول', 'مستخدم', 'كلمة', 'مرور'
        ]
        
        relevant_strings = []
        for string in strings:
            for keyword in login_keywords:
                if keyword.lower() in string.lower():
                    relevant_strings.append(string)
                    break
        
        return relevant_strings
    
    def analyze(self):
        """تحليل شامل للملف"""
        print("🔍 بدء تحليل الملف...")
        
        if not self.load_file():
            return False
        
        # معلومات أساسية
        info = self.get_file_info()
        print(f"\n📊 معلومات الملف:")
        print(f"   الحجم: {info['size']} بايت")
        print(f"   MD5: {info['md5']}")
        print(f"   SHA256: {info['sha256']}")
        print(f"   النوع: {info.get('type', 'غير معروف')}")
        
        # البحث عن نصوص تسجيل الدخول
        login_strings = self.find_login_related_strings()
        if login_strings:
            print(f"\n🔑 نصوص متعلقة بتسجيل الدخول:")
            for i, string in enumerate(login_strings[:10], 1):
                print(f"   {i}. {string}")
        
        return True

def main():
    if len(sys.argv) != 2:
        print("الاستخدام: python analyze_app.py <path_to_exe>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"الملف غير موجود: {file_path}")
        sys.exit(1)
    
    analyzer = PEAnalyzer(file_path)
    analyzer.analyze()

if __name__ == "__main__":
    main()
