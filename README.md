# مشروع تخطي تسجيل الدخول - للأغراض التعليمية

## نظرة عامة
هذا المشروع يوضح كيفية تحليل وتعديل تطبيق Windows لتخطي شاشة تسجيل الدخول باستخدام تقنيات الهندسة العكسية الأخلاقية.

## الملفات المتوفرة

### الملفات الأساسية
- `setup.exe` - الملف الأصلي
- `setup_bypassed.exe` - الملف المعدل مع تخطي تسجيل الدخول
- `setup.exe.backup` - نسخة احتياطية من الملف الأصلي

### أدوات التحليل
- `analyze_app.py` - أداة تحليل التطبيق والبحث عن نصوص تسجيل الدخول
- `bypass_login.py` - أداة تطبيق تخطي تسجيل الدخول
- `verify_bypass.py` - أداة التحقق من التعديلات
- `test_bypass.py` - أداة اختبار الملف المعدل

### التقارير والوثائق
- `reverse_engineering_guide.md` - دليل الهندسة العكسية
- `bypass_report.md` - تقرير مفصل عن التعديلات
- `README.md` - هذا الملف

## كيفية الاستخدام

### 1. تحليل التطبيق
```bash
python analyze_app.py setup.exe
```

### 2. تطبيق تخطي تسجيل الدخول
```bash
python bypass_login.py setup.exe
```

### 3. التحقق من التعديلات
```bash
python verify_bypass.py
```

### 4. اختبار الملف المعدل
```bash
python test_bypass.py setup_bypassed.exe
```

## التعديلات المطبقة

تم إجراء 5 تعديلات على الملف:

1. **الموقع 0x00000060**: تحويل JE إلى JMP
2. **الموقع 0x00000066**: تحويل JNE إلى JMP  
3. **الموقع 0x00000179**: تحويل JE إلى JMP
4. **الموقع 0x0000017C**: تحويل JE إلى JMP
5. **الموقع 0x000001A3**: تحويل JE إلى JMP

## آلية العمل

### التحليل الأولي
- فحص بنية PE للملف
- البحث عن نصوص متعلقة بتسجيل الدخول
- تحديد مواقع فحوصات المصادقة

### التعديل
- تحويل Conditional Jumps إلى Unconditional Jumps
- تخطي فحوصات المصادقة
- الحفاظ على سلامة بنية الملف

### التحقق
- مقارنة الملف الأصلي والمعدل
- توثيق جميع التغييرات
- إنشاء تقرير مفصل

## ملاحظات الأمان

⚠️ **تحذيرات مهمة:**
- هذا المشروع للأغراض التعليمية فقط
- اختبر الملفات في بيئة آمنة ومعزولة
- احتفظ بنسخة احتياطية من الملف الأصلي
- لا تستخدم هذه التقنيات على برامج لا تملكها

## المتطلبات

- Python 3.6+
- نظام Windows
- بيئة آمنة للاختبار (Virtual Machine مفضل)

## المبادئ الأخلاقية

1. **الحصول على إذن** قبل اختبار أي نظام
2. **عدم إلحاق الضرر** بالأنظمة أو البيانات
3. **الإبلاغ المسؤول** عن الثغرات
4. **احترام الخصوصية** وعدم الوصول لبيانات حساسة
5. **التعلم والتطوير** لتحسين الأمان

## التطوير المستقبلي

- إضافة دعم لأنواع أخرى من التطبيقات
- تحسين خوارزميات الكشف عن فحوصات المصادقة
- إضافة واجهة مستخدم رسومية
- دعم أنظمة تشغيل أخرى

## المساهمة

هذا مشروع تعليمي. المساهمات مرحب بها لتحسين الأدوات وإضافة ميزات جديدة.

## الترخيص

هذا المشروع للأغراض التعليمية فقط. استخدمه بمسؤولية وفقاً للقوانين المحلية.

---

**تذكر: الهدف من هذا المشروع هو التعلم وتحسين الأمان، وليس إلحاق الضرر أو انتهاك القوانين.**
