#!/usr/bin/env python3
"""
أداة التحقق من تعديلات تخطي تسجيل الدخول
"""

import os
import sys
import hashlib
from pathlib import Path

class BypassVerifier:
    def __init__(self, original_path, modified_path):
        self.original_path = original_path
        self.modified_path = modified_path
        
    def get_file_hash(self, file_path):
        """حساب hash للملف"""
        with open(file_path, 'rb') as f:
            data = f.read()
            return {
                'md5': hashlib.md5(data).hexdigest(),
                'sha256': hashlib.sha256(data).hexdigest(),
                'size': len(data)
            }
    
    def compare_files(self):
        """مقارنة الملفين"""
        print("🔍 مقارنة الملفات...")
        
        # قراءة الملفين
        with open(self.original_path, 'rb') as f:
            original_data = f.read()
        
        with open(self.modified_path, 'rb') as f:
            modified_data = f.read()
        
        if len(original_data) != len(modified_data):
            print("⚠️ أحجام الملفات مختلفة!")
            return False
        
        # العثور على الاختلافات
        differences = []
        for i, (orig_byte, mod_byte) in enumerate(zip(original_data, modified_data)):
            if orig_byte != mod_byte:
                differences.append({
                    'offset': i,
                    'original': orig_byte,
                    'modified': mod_byte,
                    'hex_offset': f"0x{i:08X}"
                })
        
        print(f"📊 تم العثور على {len(differences)} اختلاف")
        
        if differences:
            print("\n🔧 التعديلات المطبقة:")
            for i, diff in enumerate(differences[:10], 1):  # عرض أول 10 تعديلات
                print(f"   {i}. الموقع {diff['hex_offset']}: "
                      f"{diff['original']:02X} -> {diff['modified']:02X}")
                
                # تحليل نوع التعديل
                if diff['original'] == 0x74 and diff['modified'] == 0xEB:
                    print(f"      📝 تحويل JE (Jump if Equal) إلى JMP (Unconditional Jump)")
                elif diff['original'] == 0x75 and diff['modified'] == 0xEB:
                    print(f"      📝 تحويل JNE (Jump if Not Equal) إلى JMP (Unconditional Jump)")
                elif diff['original'] == 0x74 and diff['modified'] == 0x90:
                    print(f"      📝 تحويل JE إلى NOP (No Operation)")
                elif diff['original'] == 0x75 and diff['modified'] == 0x90:
                    print(f"      📝 تحويل JNE إلى NOP (No Operation)")
        
        return differences
    
    def generate_report(self):
        """إنشاء تقرير مفصل"""
        print("📋 إنشاء تقرير التحليل...")
        
        # معلومات الملفات
        orig_info = self.get_file_hash(self.original_path)
        mod_info = self.get_file_hash(self.modified_path)
        
        # مقارنة الملفات
        differences = self.compare_files()
        
        # إنشاء التقرير
        report = f"""
# تقرير تحليل تخطي تسجيل الدخول

## معلومات الملفات

### الملف الأصلي: {self.original_path}
- الحجم: {orig_info['size']} بايت
- MD5: {orig_info['md5']}
- SHA256: {orig_info['sha256']}

### الملف المعدل: {self.modified_path}
- الحجم: {mod_info['size']} بايت
- MD5: {mod_info['md5']}
- SHA256: {mod_info['sha256']}

## التعديلات المطبقة

تم إجراء {len(differences)} تعديل على الملف:

"""
        
        for i, diff in enumerate(differences, 1):
            report += f"{i}. الموقع {diff['hex_offset']}: {diff['original']:02X} -> {diff['modified']:02X}\n"
        
        report += f"""

## تحليل التعديلات

التعديلات المطبقة تهدف إلى:
1. تحويل conditional jumps إلى unconditional jumps
2. تخطي فحوصات المصادقة
3. تفعيل تسجيل الدخول التلقائي

## ملاحظات الأمان

⚠️ هذا التحليل للأغراض التعليمية فقط
⚠️ اختبر الملف في بيئة آمنة ومعزولة
⚠️ احتفظ بنسخة احتياطية من الملف الأصلي

## التوصيات

1. اختبار الملف المعدل في virtual machine
2. مراقبة سلوك التطبيق بعد التعديل
3. التأكد من عدم كسر وظائف أخرى في التطبيق
"""
        
        # حفظ التقرير
        with open('bypass_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✅ تم حفظ التقرير في: bypass_report.md")
        
        return report

def main():
    original_file = "setup.exe"
    modified_file = "setup_bypassed.exe"
    
    if not os.path.exists(original_file):
        print(f"الملف الأصلي غير موجود: {original_file}")
        sys.exit(1)
    
    if not os.path.exists(modified_file):
        print(f"الملف المعدل غير موجود: {modified_file}")
        sys.exit(1)
    
    verifier = BypassVerifier(original_file, modified_file)
    verifier.generate_report()

if __name__ == "__main__":
    main()
