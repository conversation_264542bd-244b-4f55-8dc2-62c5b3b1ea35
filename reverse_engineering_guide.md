# دليل الهندسة العكسية الأخلاقية

## الهدف
تحليل تطبيق setup.exe وتخطي شاشة تسجيل الدخول للوصول للصفحة الرئيسية مباشرة.

## معلومات الملف
- **الاسم**: setup.exe
- **النوع**: PE32 executable (GUI) Intel 80386
- **SHA256**: EC47636DE4AC43B688A74A1840E4A5E5DD116620170FEF4EDA58B675DF12A81C

## الخطوات المطلوبة

### 1. التحليل الأولي
- فحص الملف بأدوات مكافحة الفيروسات
- تحليل البنية الأساسية للملف
- فهم آلية عمل التطبيق

### 2. التحليل الديناميكي
- تشغيل التطبيق في بيئة آمنة
- مراقبة سلوك التطبيق
- تحديد نقطة فحص تسجيل الدخول

### 3. التحليل الثابت
- فحص الكود المجمع
- البحث عن دوال التحقق من المصادقة
- تحديد الشروط المنطقية للتخطي

### 4. التعديل
- تعديل الشروط المنطقية
- إنشاء patch للتخطي
- اختبار التعديلات

## الأدوات المطلوبة
1. **x64dbg** - للتصحيح الديناميكي
2. **Ghidra** - للتحليل الثابت
3. **Process Monitor** - لمراقبة النظام
4. **HxD** - محرر hex
5. **Virtual Machine** - للأمان

## ملاحظات أمنية
- استخدم بيئة افتراضية معزولة
- احتفظ بنسخة احتياطية من الملف الأصلي
- لا تشغل ملفات مجهولة على النظام الرئيسي
