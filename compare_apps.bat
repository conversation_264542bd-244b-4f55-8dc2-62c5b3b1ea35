@echo off
echo ========================================
echo مقارنة الملف الأصلي والمعدل
echo ========================================
echo.
echo هذا السكريبت سيشغل الملفين للمقارنة
echo.
echo 1. الملف الأصلي: setup.exe (مع شاشة تسجيل دخول)
echo 2. الملف المعدل: setup_bypassed.exe (بدون شاشة تسجيل دخول)
echo.
pause
echo.

echo ========================================
echo تشغيل الملف الأصلي أولاً
echo ========================================
echo.
echo سيظهر: شاشة تسجيل دخول
echo اغلق التطبيق بعد رؤية شاشة تسجيل الدخول
echo.
pause
echo.
echo تشغيل setup.exe...
powershell -Command "Start-Process 'setup.exe' -Verb RunAs"
echo.
echo انتظر حتى ترى شاشة تسجيل الدخول، ثم اغلق التطبيق
echo.
pause
echo.

echo ========================================
echo تشغيل الملف المعدل الآن
echo ========================================
echo.
echo يجب أن ترى: فتح مباشر بدون شاشة تسجيل دخول
echo.
pause
echo.
echo تشغيل setup_bypassed.exe...
powershell -Command "Start-Process 'setup_bypassed.exe' -Verb RunAs"
echo.
echo ========================================
echo النتيجة المتوقعة:
echo ========================================
echo.
echo الملف الأصلي: ظهرت شاشة تسجيل دخول ✓
echo الملف المعدل: لم تظهر شاشة تسجيل دخول ✓
echo.
echo إذا كانت النتيجة كما هو متوقع، فقد نجحت العملية!
echo.
pause
