@echo off
echo ========================================
echo تشغيل التطبيق المعدل (تخطي تسجيل الدخول)
echo ========================================
echo.
echo الملف: setup_bypassed.exe
echo الحالة: تم تخطي شاشة تسجيل الدخول
echo.
echo تحذير: تأكد من تشغيل هذا بصلاحيات المدير!
echo.
pause
echo.
echo بدء تشغيل التطبيق المعدل...
echo.

REM تشغيل التطبيق المعدل
powershell -Command "Start-Process 'setup_bypassed.exe' -Verb RunAs"

echo.
echo تم تشغيل التطبيق!
echo.
echo ما يجب أن تراه:
echo   ✅ لا توجد شاشة تسجيل دخول
echo   ✅ فتح الصفحة الرئيسية مباشرة
echo   ✅ الوصول لجميع الوظائف
echo.
echo إذا ظهرت شاشة تسجيل دخول، فهناك مشكلة في التعديل
echo.
pause
